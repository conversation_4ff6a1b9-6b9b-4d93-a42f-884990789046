#!/usr/bin/env python3
"""
SC Rabies Data Processor
Processes rabies case data for use with ArcGIS JavaScript SDK
"""

import pandas as pd
import json
from datetime import datetime, timedelta
from collections import Counter

def load_rabies_data(csv_path='Rabies25.csv'):
    """Load and clean rabies data from CSV"""
    try:
        df = pd.read_csv(csv_path)
        df.columns = ['Accession', 'Sample_Released_Date', 'County', 'Animal']
        
        # Convert date column
        df['Date'] = pd.to_datetime(df['Sample_Released_Date'])
        
        print(f"Loaded {len(df)} rabies cases")
        print(f"Date range: {df['Date'].min()} to {df['Date'].max()}")
        print(f"Counties: {df['County'].nunique()}")
        print(f"Animals: {df['Animal'].nunique()}")
        
        return df
        
    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def filter_last_30_days(df):
    """Filter data to last 30 days"""
    cutoff_date = datetime.now() - timedelta(days=30)
    filtered_df = df[df['Date'] >= cutoff_date]
    print(f"Cases in last 30 days: {len(filtered_df)}")
    return filtered_df

def get_county_case_counts(df, animal_filter=None):
    """Get case counts by county"""
    if animal_filter and animal_filter != 'all':
        df = df[df['Animal'] == animal_filter]
    
    county_counts = df['County'].value_counts().to_dict()
    return county_counts

def get_animal_summary(df):
    """Get summary of cases by animal type"""
    animal_counts = df['Animal'].value_counts().to_dict()
    return animal_counts

def export_for_web(df, output_path='rabies_summary.json'):
    """Export processed data for web application"""
    
    # Overall statistics
    stats = {
        'total_cases': len(df),
        'date_range': {
            'start': df['Date'].min().isoformat(),
            'end': df['Date'].max().isoformat()
        },
        'counties_affected': df['County'].nunique(),
        'animal_types': df['Animal'].nunique()
    }
    
    # County case counts
    county_counts = get_county_case_counts(df)
    
    # Animal type counts
    animal_counts = get_animal_summary(df)
    
    # Last 30 days data
    last_30_days = filter_last_30_days(df)
    county_counts_30_days = get_county_case_counts(last_30_days)
    
    export_data = {
        'statistics': stats,
        'county_counts': county_counts,
        'animal_counts': animal_counts,
        'last_30_days': {
            'county_counts': county_counts_30_days,
            'total_cases': len(last_30_days)
        }
    }
    
    with open(output_path, 'w') as f:
        json.dump(export_data, f, indent=2)
    
    print(f"Data exported to {output_path}")
    return export_data

def print_summary(df):
    """Print data summary"""
    print("\n=== RABIES DATA SUMMARY ===")
    print(f"Total Cases: {len(df)}")
    print(f"Date Range: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}")
    print(f"Counties Affected: {df['County'].nunique()}")
    
    print("\n=== TOP 10 COUNTIES BY CASES ===")
    county_counts = df['County'].value_counts().head(10)
    for county, count in county_counts.items():
        print(f"{county}: {count} cases")
    
    print("\n=== CASES BY ANIMAL TYPE ===")
    animal_counts = df['Animal'].value_counts()
    for animal, count in animal_counts.items():
        print(f"{animal}: {count} cases")
    
    print("\n=== LAST 30 DAYS ===")
    last_30 = filter_last_30_days(df)
    if len(last_30) > 0:
        print(f"Total cases in last 30 days: {len(last_30)}")
        county_30 = last_30['County'].value_counts().head(5)
        print("Top counties (last 30 days):")
        for county, count in county_30.items():
            print(f"  {county}: {count} cases")

if __name__ == "__main__":
    # Load and process data
    df = load_rabies_data()
    
    if df is not None:
        # Print summary
        print_summary(df)
        
        # Export for web application
        export_for_web(df)
        
        print("\nData processing complete!")
        print("Open index.html in a web browser to view the interactive map.")