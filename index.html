<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>SC Rabies Cases Interactive Map</title>
    
    <link rel="stylesheet" href="https://js.arcgis.com/4.30/esri/themes/light/main.css">
    <script src="https://js.arcgis.com/4.30/"></script>
    
    <style>
        html, body, #viewDiv {
            padding: 0;
            margin: 0;
            height: 100%;
            width: 100%;
        }
        
        #controls {
            position: absolute;
            top: 15px;
            right: 15px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 99;
            min-width: 250px;
        }
        
        #controls h3 {
            margin: 0 0 10px 0;
            color: #323232;
            font-size: 16px;
        }
        
        .filter-group {
            margin-bottom: 15px;
        }
        
        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #323232;
        }
        
        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        
        #stats {
            background: #f0f8ff;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .stat-item {
            margin: 5px 0;
            font-size: 14px;
        }
        
        .legend {
            margin-top: 15px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
        }
        
    </style>
</head>

<body>
    <div id="viewDiv"></div>
    
    <div id="controls">
        <h3>🗺️ SC Rabies Cases</h3>
        
        <div class="filter-group">
            <label for="animalFilter">Filter by Animal:</label>
            <select id="animalFilter">
                <option value="all">All Animals</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label for="dateRange">Date Range:</label>
            <select id="dateRange">
                <option value="30">Last 30 Days</option>
                <option value="60">Last 60 Days</option>
                <option value="90">Last 90 Days</option>
                <option value="all">All Time</option>
            </select>
        </div>
        
        <div id="stats">
            <div class="stat-item"><strong>Total Cases:</strong> <span id="totalCases">0</span></div>
            <div class="stat-item"><strong>Counties Affected:</strong> <span id="countiesAffected">0</span></div>
            <div class="stat-item"><strong>Date Range:</strong> <span id="dateRangeText">-</span></div>
        </div>
        
        <div class="legend">
            <strong>Cases per County:</strong>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ffffcc;"></div>
                <span>1-2 cases</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ffeda0;"></div>
                <span>3-5 cases</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fed976;"></div>
                <span>6-10 cases</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #feb24c;"></div>
                <span>11-20 cases</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fd8d3c;"></div>
                <span>21+ cases</span>
            </div>
        </div>
    </div>

    <script>
        require([
            "esri/Map",
            "esri/views/MapView",
            "esri/layers/GraphicsLayer",
            "esri/Graphic",
            "esri/symbols/SimpleFillSymbol",
            "esri/symbols/SimpleLineSymbol",
            "esri/PopupTemplate",
            "esri/renderers/ClassBreaksRenderer",
            "esri/geometry/Polygon"
        ], function(Map, MapView, GraphicsLayer, Graphic, SimpleFillSymbol, SimpleLineSymbol, PopupTemplate, ClassBreaksRenderer, Polygon) {

            let rabiesData = [];
            let countyCaseCounts = {};
            let filteredData = [];
            let countyLayer;
            let countyGeoData = null;

            // Initialize the map
            const map = new Map({
                basemap: "streets-navigation-vector"
            });

            const view = new MapView({
                container: "viewDiv",
                map: map,
                center: [-81.1637, 33.8361], // South Carolina center
                zoom: 7
            });

            // Load CSV data
            async function loadCSVData() {
                try {
                    const response = await fetch('Rabies25.csv');
                    const csvText = await response.text();

                    const lines = csvText.split('\n');
                    const headers = lines[0].split(',');

                    rabiesData = [];
                    for (let i = 1; i < lines.length; i++) {
                        if (lines[i].trim()) {
                            const values = lines[i].split(',');
                            if (values.length >= 4) {
                                rabiesData.push({
                                    accession: values[0],
                                    date: new Date(values[1]),
                                    county: values[2],
                                    animal: values[3]
                                });
                            }
                        }
                    }

                    console.log('Loaded', rabiesData.length, 'rabies cases');
                    populateAnimalFilter();
                    filterData();

                } catch (error) {
                    console.error('Error loading CSV:', error);
                }
            }

            // Load GeoJSON county data
            async function loadCountyGeoData() {
                try {
                    const response = await fetch('SC_CountiesJSON.geojson');
                    countyGeoData = await response.json();
                    console.log('Loaded county GeoJSON data with', countyGeoData.features.length, 'counties');

                    // Initialize the county layer after loading geo data
                    initializeCountyLayer();

                } catch (error) {
                    console.error('Error loading county GeoJSON:', error);
                }
            }
            
            // Populate animal filter dropdown
            function populateAnimalFilter() {
                const animals = [...new Set(rabiesData.map(d => d.animal))].sort();
                const select = document.getElementById('animalFilter');
                
                // Clear existing options except "All Animals"
                while (select.children.length > 1) {
                    select.removeChild(select.lastChild);
                }
                
                animals.forEach(animal => {
                    const option = document.createElement('option');
                    option.value = animal;
                    option.textContent = animal;
                    select.appendChild(option);
                });
            }
            
            // Filter data based on current selections
            function filterData() {
                const animalFilter = document.getElementById('animalFilter').value;
                const dateRangeFilter = document.getElementById('dateRange').value;
                
                let filtered = rabiesData;
                
                // Filter by animal
                if (animalFilter !== 'all') {
                    filtered = filtered.filter(d => d.animal === animalFilter);
                }
                
                // Filter by date range
                if (dateRangeFilter !== 'all') {
                    const daysBack = parseInt(dateRangeFilter);
                    const cutoffDate = new Date();
                    cutoffDate.setDate(cutoffDate.getDate() - daysBack);
                    filtered = filtered.filter(d => d.date >= cutoffDate);
                }
                
                filteredData = filtered;
                calculateCountyCounts();
                updateStats();
                updateCountyData();
            }
            
            // Calculate case counts per county
            function calculateCountyCounts() {
                countyCaseCounts = {};
                filteredData.forEach(d => {
                    const county = d.county;
                    countyCaseCounts[county] = (countyCaseCounts[county] || 0) + 1;
                });
                
                console.log('Calculated case counts:', countyCaseCounts);
                console.log('Total counties with cases:', Object.keys(countyCaseCounts).length);
                console.log('Total cases:', Object.values(countyCaseCounts).reduce((a, b) => a + b, 0));
            }
            
            // Update statistics display
            function updateStats() {
                document.getElementById('totalCases').textContent = filteredData.length;
                document.getElementById('countiesAffected').textContent = Object.keys(countyCaseCounts).length;
                
                if (filteredData.length > 0) {
                    const dates = filteredData.map(d => d.date).sort();
                    const minDate = dates[0].toLocaleDateString();
                    const maxDate = dates[dates.length - 1].toLocaleDateString();
                    document.getElementById('dateRangeText').textContent = `${minDate} - ${maxDate}`;
                } else {
                    document.getElementById('dateRangeText').textContent = 'No data';
                }
            }
            
            // Get color based on case count - very bright colors for visibility
            function getColor(count) {
                if (count >= 21) return [255, 0, 0, 1.0];     // Bright red
                if (count >= 11) return [255, 100, 0, 1.0];   // Orange red  
                if (count >= 6) return [255, 165, 0, 1.0];    // Orange
                if (count >= 3) return [255, 255, 0, 1.0];    // Bright yellow
                if (count >= 1) return [173, 255, 47, 1.0];   // Green yellow
                return [200, 200, 200, 0.2]; // Light gray for no cases
            }
            
            // Initialize county layer using GraphicsLayer
            function initializeCountyLayer() {
                if (!countyGeoData) {
                    console.error("County GeoJSON data not loaded yet");
                    return;
                }

                console.log("Initializing county layer from GeoJSON data...");

                // Create a graphics layer for counties
                countyLayer = new GraphicsLayer({
                    title: "SC Counties"
                });

                map.add(countyLayer);

                // Create graphics for each county
                updateCountyData();
            }
            
            // Update county data with case counts
            function updateCountyData() {
                if (!countyLayer || !countyGeoData) return;

                console.log('Updating county data with case counts:', countyCaseCounts);

                // Clear existing graphics
                countyLayer.removeAll();

                // Create graphics for each county
                countyGeoData.features.forEach(feature => {
                    // Get county name from properties
                    let countyName = feature.properties.name || feature.properties.NAME || feature.properties.COUNTY_NAM || feature.properties.NAME10;

                    if (countyName) {
                        countyName = countyName.replace(' County', '').trim();
                    }

                    const caseCount = countyCaseCounts[countyName] || 0;
                    console.log(`County: ${countyName}, Cases: ${caseCount}`);

                    // Get color based on case count
                    const color = getColor(caseCount);

                    // Create symbol
                    const symbol = new SimpleFillSymbol({
                        color: color,
                        outline: new SimpleLineSymbol({
                            color: [128, 128, 128],
                            width: 1
                        })
                    });

                    // Create polygon geometry
                    const polygon = new Polygon({
                        rings: feature.geometry.coordinates,
                        spatialReference: { wkid: 4326 }
                    });

                    // Create graphic
                    const graphic = new Graphic({
                        geometry: polygon,
                        symbol: symbol,
                        attributes: {
                            NAME: countyName,
                            CASE_COUNT: caseCount
                        },
                        popupTemplate: {
                            title: "{NAME} County",
                            content: "<b>Rabies Cases:</b> {CASE_COUNT}<br><em>Based on current filter settings</em>"
                        }
                    });

                    countyLayer.add(graphic);
                });

                console.log(`Added ${countyGeoData.features.length} county graphics to the map`);
            }
            
            
            // Event listeners
            document.getElementById('animalFilter').addEventListener('change', filterData);
            document.getElementById('dateRange').addEventListener('change', filterData);
            
            
            // Initialize everything when view is ready
            view.when(() => {
                loadCSVData();
                loadCountyGeoData();
            });
        });
    </script>
</body>
</html>