# SC Rabies Cases Interactive Map

An interactive web application that visualizes rabies cases across South Carolina counties using the ArcGIS JavaScript SDK.

## Features

- 🗺️ **Interactive Map**: Pan, zoom, and explore SC counties with rabies case data
- 📊 **County-Level Visualization**: See total affected animals by county with color-coded choropleth
- 🔍 **Animal Filter**: Filter cases by specific animal types (<PERSON><PERSON><PERSON>, <PERSON>, Skunk, Fox, Bat, etc.)
- 📅 **Date Range Filter**: View data for last 30, 60, 90 days, or all time
- 📈 **Live Statistics**: Real-time stats showing total cases and affected counties
- 🎨 **Color-Coded Legend**: Easy-to-read legend showing case count ranges

## Files

- `index.html` - Main interactive web application
- `Rabies25.csv` - Raw rabies case data
- `data_processor.py` - Python script for data analysis
- `serve.py` - Local web server to run the application
- `SC_CountiesJSON.geojson` - South Carolina county boundaries (optional)

## Quick Start

### Method 1: Using Python Server (Recommended)
```bash
python3 serve.py
```
This will start a local server and automatically open your web browser to view the map.

### Method 2: Direct File Opening
Simply open `index.html` in a modern web browser. Note: Some browsers may have CORS restrictions when loading local CSV files.

## Data Source

The application uses:
- **Rabies Case Data**: `Rabies25.csv` containing Accession #, Sample Released Date, County, and Animal
- **County Boundaries**: ArcGIS Feature Service at `https://services5.arcgis.com/G4BLIH7rTQoIjCFv/arcgis/rest/services/SC_Counties/FeatureServer`

## Usage

1. **Map Navigation**: Use mouse to pan and zoom the map
2. **Filter by Animal**: Select specific animal types from the dropdown
3. **Filter by Date**: Choose different time ranges to view recent vs. historical data
4. **County Details**: Click on any county to see popup with case count
5. **Statistics Panel**: View real-time statistics in the control panel

## Data Analysis

Run the Python data processor for detailed statistics:
```bash
python3 data_processor.py
```

This will show:
- Total cases and date range
- Top counties by case count
- Breakdown by animal type
- Last 30 days summary
- Export JSON summary for other uses

## Technical Details

- **Frontend**: ArcGIS JavaScript SDK 4.30
- **Basemap**: Streets Navigation Vector
- **Renderer**: Class Breaks Renderer with 5 color categories
- **Data Processing**: Client-side CSV parsing and filtering
- **County Service**: Esri Feature Service for SC county boundaries

## Browser Requirements

- Modern web browser with JavaScript enabled
- Internet connection (for loading ArcGIS SDK and basemaps)

## Color Legend

- 🟡 **Light Yellow**: 1-2 cases
- 🟠 **Light Orange**: 3-5 cases  
- 🟠 **Orange**: 6-10 cases
- 🔴 **Dark Orange**: 11-20 cases
- 🔴 **Red**: 21+ cases

## Data Summary

Current dataset includes:
- **63 total cases** from January 2025 to September 2025
- **23 counties affected**
- **6 animal types**: Raccoon (25), Cat (10), Skunk (10), Fox (10), Bat (7), Wild Other (1)
- **Top counties**: Anderson (8), Charleston (6), York (6)

---

Built with ❤️ using ArcGIS JavaScript SDK